<script lang="ts" setup>
import { reactive, ref, onMounted, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

import { TENANT_LIST_TABLE_COLUMNS, TENANT_LIST_TABLE_COLUMNS_FORADMIN } from '@/js/table-options'

import systemService from '@/service/systemService'
import permissionService from '@/service/permissionService'
import aicService from '@/service/aicService'

import type { ILeadColumn } from '@/types/lead'
import type { IGetCrmLeadParams } from '@/types/lead'
import type { TableInstance } from 'element-plus'
import type { ITenantPageParams, ITenantPageItem } from '@/types/tenant'
import type { RootState } from '@/types/store'

import { ElMessage } from 'element-plus'
import SearchBox from '@/components/common/SearchBox.vue'
import TenantForm from './components/TenantForm.vue'
import EquitiesDetail from './components/EquitiesDetail.vue'
import OutboundServiceSetting from './components/OutboundServiceSetting.vue'

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})
const updateSearchParams = (params: IGetCrmLeadParams) => {
    queryParams = params
    search()
}
const router = useRouter()
const store = useStore<RootState>()
const isAdmin = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { tenantId } = user || {}
    if (tenantId === '') {
        return true
    } else {
        return false
    }
})

const isYunwei = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { role } = user || {}
    return role?.includes('yunwei')
})


onMounted(async () => {
    if(isPlatManager.value){
        searchOptionKey.value = 'TENANT_SEARCH_OPTIONS_FORADMIN'
    }
    if(isYunwei.value || isAdmin.value){
        tableAllOptions.value = TENANT_LIST_TABLE_COLUMNS_FORADMIN
    }
    getTableHeight()
    search()
})
const searchOptionKey = ref('TENANT_SEARCH_OPTIONS')

const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - actionBarContentRef.value.clientHeight - 32 - 16 - 16 - 16 -16 - 16 - 16
    }
}

//---------------table-----------------//
const tableHeight = ref(500)
const tableData = ref<ITenantPageItem[]>([])
const tableAllOptions = ref<ILeadColumn[]>(TENANT_LIST_TABLE_COLUMNS)
const tableLoading = ref(false)
const tableListRef = ref<TableInstance>()
let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

let queryParams = reactive<ITenantPageParams>({
    page: 1,
    pageSize: 20,
})
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    search()
}
const search = async () => {
    tableLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    console.log('搜索的请求', queryParams)
    let searchRes = await systemService.tenantPage(queryParams)
    console.log('搜索的结果', searchRes)
    if (searchRes.errCode === 0) {
        tableLoading.value = false
        tableData.value = searchRes.data ? searchRes.data : []
        pageInfo.total = searchRes.total
    }
}
const handleCloseFormVisible = () => {
    tenantFormVisible.value = false
    search()
}
const handleCloseEquitiesDetail = () => {
    showEquitiesDetail.value = false
}

/* 操作区域逻辑 */
const tenantFormVisible = ref(false)
const tenantFormType = ref('add')
const showEquitiesDetail = ref(false)
const OutboundServiceSettingVisible = ref(false)
const editValue = ref<Partial<ITenantPageItem>>({})
const chooseTenantInfo = ref<Partial<ITenantPageItem>>({})
const handleAdd = () => {
    tenantFormType.value = 'add'
    editValue.value = {}
    tenantFormVisible.value = true
}
const handleEdit = (row: ITenantPageItem) => {
    tenantFormType.value = 'edit'
    editValue.value = row
    tenantFormVisible.value = true
}
const handleChangeStatus = async (row: ITenantPageItem, status: 1 | 0) => {
    // 修改租户状态
    let obj = {
        ids: [row.id],
        status: status,
    }
    let res = await systemService.tenantDisable(obj)
    if (res.errCode === 0) {
        ElMessage.success('修改状态成功')
    } else {
        ElMessage.error('修改状态失败')
    }
    search()
}
const handleEquitiesDetail = (row: ITenantPageItem) => {
    editValue.value = row
    showEquitiesDetail.value = true
    console.log('row', row)
}

const handleMemberManagement = (row: ITenantPageItem) => {
    console.log('row', row)
    router.push({
        name: 'mail-list',
        params: {
            type: row.id,
            name: row.name
        },
    })
}

const handleOutboundServiceSetting = (row: ITenantPageItem) => {
    chooseTenantInfo.value = row
    OutboundServiceSettingVisible.value = true
}
watch(() => OutboundServiceSettingVisible.value, (newVal) => {
    if (!newVal) {
        chooseTenantInfo.value = {}
    }
})

// 申请的小号数量
const phoneCount = ref(1)
const confirmLoading = ref(false)
const submit = () => {
    confirmLoading.value = true
    // console.log('提交',phoneCount.value)
    aicService.applyWorkNumber({bsxhNum:phoneCount.value,thirdSign:'qxy',useType:1,tenantId:tenantId.value}).then((res) => {
        console.log('申请结果',res)
        if(res.success){
            ElMessage.success('提交成功')
        }else{
            ElMessage.error(res.errMsg)
        }
    }).finally(() => {
        confirmLoading.value = false
        handleApplyClose()
    }) 
}
// 申请小号
const tenantId = ref('')
const applyWorkNumber = (row: ITenantPageItem) => {
    console.log('applyWorkNumber',row)
    tenantId.value = row.id
    applyVisible.value = true
}
const applyVisible = ref(false)
const handleApplyClose = () => {
    applyVisible.value = false
}

</script>
<template>
    <div ref="mainContentRef" class="height-100 oa">
        <!-- 搜索栏 -->
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff;">
            <SearchBox
                :searchOptionKey="searchOptionKey"
                @updateSearchParams="updateSearchParams"
            ></SearchBox>
        </div>
        <!-- 表格栏 -->
        <div class="all-padding-16" style="background-color: #fff; box-sizing: border-box">
            <!-- 工具条 -->
            <div ref="actionBarContentRef" class="display-flex top-bottom-center action-bar" >
                <el-button v-if="permissionService.isTenantAddPermitted()" type="primary" @click="handleAdd()">新增</el-button>
            </div>
            <!-- 表格 -->
            <el-table
                ref="tableListRef"
                :height="tableHeight+'px'"
                :data="tableData"
                v-loading="tableLoading"
                row-key="id"
                header-row-class-name="tableHeader"
            >
                <el-table-column
                    v-for="columns in tableAllOptions.filter((item) => item.isShow === true)"
                    :key="columns.key"
                    :prop="columns.prop"
                    :label="columns.label"
                    :width="columns.width"
                    :type="columns.type"
                    :fixed="columns.fixed"
                    :sortable="columns.sortable"
                    show-overflow-tooltip
                >
                    <template #default="scope">
                        <el-dropdown  v-if="columns.prop === 'status'" placement="bottom-end" trigger="click">
                            <el-tag class="pointer" effect="plain" :type="scope.row.status === 1 ? 'success' : 'info'">
                                {{ scope.row.status === 1 ? '启用' : '停用' }} 
                                <el-icon>
                                    <ArrowDown />
                                </el-icon>
                            </el-tag>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        style="padding: 0 26px"
                                        v-if="scope.row.status === 0"
                                        @click="handleChangeStatus(scope.row, 1)"
                                    >启用</el-dropdown-item>
                                    <el-dropdown-item
                                        style="padding: 0 26px"
                                        v-if="scope.row.status === 1"
                                        @click="handleChangeStatus(scope.row, 0)"
                                    >停用</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                        <div v-if="columns.prop === 'action'" class="display-flex top-bottom-center gap-4">
                            <a
                                class="el-dropdown-link"
                                style="text-decoration: none"
                                @click="handleEquitiesDetail(scope.row)"
                            >权益明细</a>
                            <a
                                class="el-dropdown-link"
                                style="text-decoration: none"
                                @click="applyWorkNumber(scope.row)"
                            >申请小号</a>
                            <el-dropdown v-if="isAdmin" trigger="click">
                                <a class="el-dropdown-link" style="text-decoration: none">更多</a>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item>
                                            <el-button
                                                class="r-margin-10"
                                                type="primary"
                                                text
                                                @click="handleEdit(scope.row)"
                                            >编辑</el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item>
                                            <el-button
                                                class="r-margin-10"
                                                type="primary"
                                                text
                                                @click="handleMemberManagement(scope.row)"
                                            >成员管理</el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item>
                                            <el-button
                                                class="r-margin-10"
                                                type="primary"
                                                text
                                                @click="handleOutboundServiceSetting(scope.row)"
                                            >智能外呼</el-button>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>

                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-affix position="bottom" :offset="0">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
        <TenantForm
            v-if="tenantFormVisible"
            :visible="tenantFormVisible"
            @closeVisible="handleCloseFormVisible()"
            :editValue="editValue"
            :from="tenantFormType"
            :isAdmin="isAdmin"
        ></TenantForm>
        <EquitiesDetail 
            v-if="showEquitiesDetail"
            :visible="showEquitiesDetail"
            :tenantInfo="editValue"
            @closeVisible="handleCloseEquitiesDetail()"
        ></EquitiesDetail>
        <OutboundServiceSetting v-model:visible="OutboundServiceSettingVisible" :tenantInfo="chooseTenantInfo"></OutboundServiceSetting>
    </div>
    <el-dialog
        v-model="applyVisible"
        title="申请小号"
        width="500px"
        @close="handleApplyClose"
    >
        <div class="">
            <div class="display-flex flex-column gap-8">
                <div class="display-flex top-bottom-center gap-12">
                    <div class="color-red">*</div>
                    <div>选择数量</div>
                </div>
                <el-input-number v-model="phoneCount" :max="200" :min="1" />
            </div>
            <div class="display-flex flex-end">
                <el-button
                    class="l-margin-12 all-padding-16"
                    @click="handleApplyClose"
                >取消</el-button>
                <el-button
                    :loading="confirmLoading"
                    type="primary"
                    class="all-padding-16"
                    @click="submit"
                >提交</el-button>
            </div>
        </div>
    </el-dialog>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
.action-bar {
    color: #303133;
    margin-bottom: 16px;
    flex-direction: row-reverse;
    .choose-content {
        font-size: 14px;
        color: #a6a6a6;
        margin-right: 16px;
    }
    .turn-content {
        margin-right: 16px;
    }
}

.el-dropdown-link {
    display: block;
    cursor: pointer;
    color: var(--main-blue-);
    font-size: 16px;
}
:deep(.el-dropdown-menu__item) {
    padding: 2px 16px;
}
:deep(.el-table.is-scrolling-left th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-table__header-wrapper tr th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-button) {
    font-weight: 400;
}

.tableHeader {
    font-size: 24px;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>
