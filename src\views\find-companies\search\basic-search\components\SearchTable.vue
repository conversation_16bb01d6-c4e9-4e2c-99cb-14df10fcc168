<script setup lang="ts">
import CompanyDetailDrawer from '@/components/search/company-detail-drawer/company-detail-drawer.vue'
import TransferCrmDialog from '@/components/transfer-crm-dialog/TransferCrmDialog.vue'
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
import type { ICompanyInfo, IGetCompanyByCodeParams } from '@/types/company'
import type { RootState } from '@/types/store'
import { formatFilters } from '@/utils/enterprise/filters'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useStore } from 'vuex'
import SearchResultDisplay from './SearchResultDisplay.vue'
import permissionService from '@/service/permissionService'

const props = defineProps<{
    keyword: string
    scope: string
    externalParams: Record<string, string>
}>()
const multipleSelection = ref<ICompanyInfo[]>([])
const companyDetailDrawerVisible = ref(false)
const store = useStore<RootState>()
const list = ref<ICompanyInfo[]>([])
const loading = ref(false)
const searchchannellype = ref(0)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    realTotal: 0,
})
const requestParams = ref<IGetCompanyByCodeParams>({
    keyword: props.keyword,
    matchType: 'most_fields',
    scope: props.scope,
})
const tableRef = ref()

const filterParams = ref<{ [x: string]: string | string[] }>({})

const socialCreditCode = ref('')
const transferCrmDialogVisible = ref(false)

const toDetail = (row: ICompanyInfo) => {
    if (row.socialCreditCode) {
        socialCreditCode.value = row.socialCreditCode
        companyDetailDrawerVisible.value = true
    } else {
        ElMessage.error('该企业暂无详情')
    }
}

const handlerOpenUrl = (url: string) => {
    if (url) {
        if (!/^https?:\/\//.test(url)) {
            url = `https://${url}`
        }
        window.open(url, '_blank')
    }
}

const getData = () => {
    const { scope, keyword } = requestParams.value || {}

    if (scope && scope !== '0' && keyword === '') {
        ElMessage.warning('搜索范围需要配合关键词搜索，请输入关键词')
        return
    }

    // if (loading.value) return
    loading.value = true
    aicService
        .searchEnterprise({
            ...requestParams.value,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            ...filterParams.value,
        })
        .then((res) => {
            loading.value = false
            const { errCode, data, total, channelType, realTotal } = res
            if (errCode === 0) {
                list.value = data
                pageInfo.value.total = total
                pageInfo.value.realTotal = realTotal
                searchchannellype.value = channelType
            } else {
                pageInfo.value.total = 0
                pageInfo.value.realTotal = 0
                list.value = []
            }
        })
        .catch((error) => {
            const { isCanceled } = error
            if (!isCanceled) {
                loading.value = false
                pageInfo.value.total = 0
                pageInfo.value.realTotal = 0
                list.value = []
            }
        })
}

const searchParams = computed(() => {
    const { normalFilterParams } = store.state.enterprise

    return normalFilterParams
})

onMounted(() => {
    // getData()
})

watch(
    () => searchParams,
    (value) => {
        const formattedData = formatFilters(value.value)
        filterParams.value = formattedData
        getData()
    },
    { deep: true }
)

watch(
    () => props.scope,
    (value) => {
        requestParams.value = { ...requestParams.value, scope: value }
    }
)

watch(
    () => props.keyword,
    (value) => {
        requestParams.value = { ...requestParams.value, keyword: value }
        // getData()
    }
)

watch(
    () => props.externalParams,
    (value) => {
        requestParams.value = { ...requestParams.value, ...value }
        getData()
    }
)

const sortList = [
    {
        label: '默认排序',
        value: 0,
    },
    {
        label: '成立时间-降序',
        value: 1,
    },
    {
        label: '成立时间-升序',
        value: 2,
    },
    {
        label: '注册资本-降序',
        value: 3,
    },
    {
        label: '注册资本-升序',
        value: 4,
    },
]

const onSortChange = (v: number) => {
    requestParams.value = { ...requestParams.value, sortBy: v }
    getData()
}

const handleSelectionChange = (val: ICompanyInfo[]) => {
    console.log('val', val)
    multipleSelection.value = val
}

const transferSelected = () => {
    if (multipleSelection.value.length === 0) {
        ElMessage.warning('请至少选择一条数据')
        return
    }
    transferCrmDialogVisible.value = true
}

const selectable = (row: ICompanyInfo) => {
    return row.socialCreditCode
}

const getPhone = (row: ICompanyInfo) => {
    ElMessageBox.confirm('查看联系方式将会扣除对应线索权益额度，是否确定查看？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        console.log('确认查看' + row.companyName)
        orderService
            .orderBuyLegal({
                serviceKey: 'xs',
                socialCreditCode: row.socialCreditCode,
                companyName: row.companyName,
            })
            .then((res) => {
                const { contacts } = res
                row.isBuy = true
                if (contacts && Array.isArray(contacts) && contacts.length > 0) {
                    const { content } = contacts[0] || {}
                    row.contact = content
                }
                ElMessage.success('已成功获取企业号码')
            })
    })
}

const transferCrmSuccess = () => {
    if (tableRef.value) {
        tableRef.value.clearSelection()
        getData()
    }
}
</script>

<template>
    <div class="flex flex-column height-100 basic-search-table">
        <div class="flex flex-1 flex-column gap-16" style="overflow: hidden">
            <div class="flex flex-row gap-16 top-bottom-center">
                <div class="flex flex-row gap-8 top-bottom-center">
                    <div class="font-14">
                        共
                        <span class="color-blue lr-padding-2 font-weight-600">
                            {{ pageInfo.realTotal }}
                        </span>
                        结果
                    </div>
                    <div class="w-1 back-color-border h-12"></div>
                    <div class="font-14">
                        已选<span class="color-blue lr-padding-2 font-weight-600">{{ multipleSelection.length }}</span
                        >个
                    </div>
                </div>
                <div class="flex flex-row gap-16">
                    <el-dropdown v-if="permissionService.isTransferNewLeadPermitted()" placement="bottom-start">
                        <el-button>
                            转CRM
                            <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="transferSelected">转移所选</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <el-dropdown placement="bottom-start">
                        <el-button>
                            <el-icon><Sort /></el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    v-for="item in sortList"
                                    :key="item.value"
                                    @click="onSortChange(item.value)"
                                >
                                    {{ item.label }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>

            <el-table
                ref="tableRef"
                :data="list"
                style="width: 100%"
                v-loading="loading"
                @selection-change="handleSelectionChange"
                height="100%"
                :default-expand-all="true"
            >
                <el-table-column type="selection" :selectable="selectable" width="55" />
                <el-table-column label="号码" width="130" v-if="searchchannellype === 1">
                    <template #default="props">
                        <div v-if="props.row.isBuy">
                            <span v-if="props.row.hasmobile === '1'">{{ props.row.contact }}</span>
                            <span v-else>暂无联系方式</span>
                        </div>
                        <template v-else>
                            <div
                                v-if="props.row.socialCreditCode && props.row.hasmobile === '1'"
                                class="color-blue pointer"
                                @click="getPhone(props.row)"
                            >
                                获取号码
                            </div>
                            <div v-else>暂无联系方式</div>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column type="expand" width="1">
                    <template #default="props">
                        <div class="flex t-padding-4 b-padding-12 l-padding-197">
                            <SearchResultDisplay :row="props.row" :keyword="keyword" :scope="scope" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="公司名称">
                    <template #default="props">
                        <div class="pointer" @click="toDetail(props.row)" v-html="props.row.companyname_ws"></div>
                        <div class="t-margin-12 font-14">
                            {{ props.row.legalperson || '-' }} | {{ props.row.esdate || '-' }} |
                            {{ props.row.regCapDisplay || '-' }}
                        </div>
                    </template>
                </el-table-column>

                <!-- <el-table-column label="法定代表人" width="120" prop="legalperson">
                    <template #default="props">
                        <div class="pointer" @click="toDetail(props.row)">{{ props.row.legalperson }}</div>
                    </template>
                </el-table-column> -->

                <!-- <el-table-column label="成立日期" width="120">
                    <template #default="props">
                        <div class="flex top-bottom-center pointer" @click="toDetail(props.row)">
                            <div>{{ props.row.esdate ? moment(props.row.esdate).format('YYYY-MM-DD') : '-' }}</div>
                        </div>
                    </template>
                </el-table-column> -->

                <el-table-column label="状态" width="250" prop="">
                    <template #default="props">
                        <el-tag
                            class="pointer"
                            v-if="props.row.entstatus"
                            effect="plain"
                            :type="
                                props.row.companyTags.find((item) => {
                                    return item.categoryCode === '001'
                                }).tagName == '注销'
                                    ? 'danger'
                                    : 'success'
                            "
                            @click="toDetail(props.row)"
                        >
                            {{
                                props.row.companyTags.find((item) => {
                                    return item.categoryCode === '001'
                                })?.tagName || '-'
                            }}
                        </el-tag>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="注册资金" prop="regCapDisplay">
                    <template #default="props">
                        <div class="pointer" @click="toDetail(props.row)">{{ props.row.regCapDisplay }}</div>
                    </template>
                </el-table-column> -->
                <el-table-column label="网站">
                    <template #default="props">
                        <div class="main-color pointer" @click="handlerOpenUrl(props.row.officialWebsite)">
                            {{ props.row.officialWebsite || '--' }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="通讯地址" prop="contactaddress">
                    <template #default="props">
                        <div class="pointer" @click="toDetail(props.row)">{{ props.row.contactaddress }}</div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-affix position="bottom" :offset="0">
            <div class="flex top-bottom-center all-padding-18 justify-flex-end back-color-white">
                <el-pagination
                    :hide-on-single-page="true"
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="getData"
                    @current-change="getData"
                />
            </div>
        </el-affix>
        <CompanyDetailDrawer
            v-model:drawer="companyDetailDrawerVisible"
            v-if="companyDetailDrawerVisible"
            :socialCreditCode="socialCreditCode"
            @refreshList="getData"
        />
        <TransferCrmDialog
            v-model:visible="transferCrmDialogVisible"
            :selected="multipleSelection"
            :success="transferCrmSuccess"
        />
    </div>
</template>

<style scoped>
:deep(.el-tooltip__trigger) {
    outline: none;
}

.basic-search-table :deep(.el-table__row) td {
    border-bottom: none;
}

.basic-search-table :deep(.el-table__expanded-cell) {
    padding: 0;
}

.basic-search-table :deep(.el-table__expand-icon) {
    visibility: hidden !important;
}

.basic-search-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
