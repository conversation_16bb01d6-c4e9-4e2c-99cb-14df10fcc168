<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'

import type { ILeadTransfelParams, SearchPoolListResponseItem } from '@/types/lead'
import type { FormInstance, FormRules } from 'element-plus'
import type { IGetOrgNames, IUserAccountInfoUser} from '@/types/user'
import type { RootState } from '@/types/store'

import crmService from '@/service/crmService'
import systemService from '@/service/systemService'

import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'

const store = useStore<RootState>()
const formRef = ref<FormInstance>()
const props = defineProps<{
    visible: boolean
    checkedIds: string[]
    fromCustomer?: boolean
    poolId: string
}>()
const dialogVisible = ref(props.visible)
const userInfo = ref<IUserAccountInfoUser>()

watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
        const { account } = store.state.user || {}
        const { user } = account || {}
        userInfo.value = user
    }
},
{
    immediate: true
})
const poolInfo=ref<SearchPoolListResponseItem>()
const getPoolInfo = async (id: string) => {
    let res = await crmService.crmManageDetail({ poolId: id })
    res.users = res.users.filter((item) => item.status === '0')
    console.log(res.users)
    poolInfo.value = res
}
watch(() => props.poolId, (newVal) => {
    if (newVal) {
        getPoolInfo(newVal)
    }
}, {
    immediate: true
})

const form = reactive({
    targetUserId: '',
    orgId: '',
})
const GetUserOrg = (id: string) => {
    systemService.userGetUserOrg({ id: id }).then((res) => {
        orgList.value = res.orgs
        if (orgList.value.length === 1) {
            form.orgId = orgList.value[0].id
        }
    })
}
const orgList = ref<IGetOrgNames[]>()

watch(() => form.targetUserId, async (newVal) => {
    form.orgId = ''
    if (newVal) {
        await GetUserOrg(newVal)
    }
})
const emit = defineEmits(['closeVisible'])

const rules = reactive<FormRules>({
    targetUserId: [{ required: true, message: '请选择新负责人', trigger: 'blur' }],
    orgId: [{ required: true, message: '请选择所属组织', trigger: 'blur' }],
})

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            let obj: ILeadTransfelParams = {
                ids: props.checkedIds,
                transferType: 3,
                targetUserId: form.targetUserId,
                orgId: form.orgId ? form.orgId : userInfo.value?.defaultOrg as string,
            }
            if (props.fromCustomer) { // 如果是从客户公海传出的
                obj.fromCustomerOpenSea = true
                obj.customerPoolId = poolInfo.value?.id
            }
            crmService.crmTransfer(obj).then(() => {
                ElMessage.success('转移成功')
                handleClose()
            })
        } else {
            console.log('form表单效验不通过', fields)
        }
    })
}
const handleClose = (val?: string) => {
    form.targetUserId = ''
    form.orgId = ''
    emit('closeVisible',val)
}
const handleCancel = () => {
    dialogVisible.value = false
}
</script>
<template>
    <el-dialog
        header-class="dialog-custom-header"
        v-model="dialogVisible"
        :title="props.fromCustomer? '转移客户' : '转移线索'"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose('cancel')"
    >
        <el-form class="tenant-form" ref="formRef" :model="form" label-position="top" :rules="rules">
            <el-form-item label="新负责人" prop="targetUserId">
                <el-select placeholder="请选择新负责人" v-model="form.targetUserId">
                    <el-option v-for="user in poolInfo?.users" :key="user.id" :label="user.nickname" :value="user.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="form.targetUserId && orgList?.length && orgList?.length>1" label="线索所属组织" prop="orgId">
                <el-select placeholder="请选择线索所属组织" v-model="form.orgId">
                    <el-option v-for="org in orgList" :key="org.id" :label="org.name" :value="org.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleCancel()">取消</el-button>
                    <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang="scss">
</style>
