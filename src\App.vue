<template>
    <div id="app">
        <!-- 全局头部 -->
        <!-- 路由视图 -->
        <router-view v-if="isRouterAlive && !isMaintenance" />
        <div v-else>系统正在维护</div>
        <!-- 全局页脚 -->
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, provide, ref } from 'vue'
import { loadIconfont } from './utils/loadIconfont'
import { loadStatus } from './utils/loadstatus'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const store = useStore<RootState>()
const logout = async () => {
    store.dispatch('auth/logout')
}
const isRouterAlive = ref(true)
const isMaintenance = ref(false)

const reload = () => {
    console.log('重载页面')
    isRouterAlive.value = false //先关闭，
    nextTick(() => {
        isRouterAlive.value = true
    })
}

const getStatus = async () => {
    const status = await loadStatus()
    const { maintenance } = status || {}
    isMaintenance.value = maintenance
}

provide('reload', reload)

onMounted(() => {
    window.addEventListener('storage', (e) => {
        console.log('storage', e)  
        if (e.key === 'refresh_token' && e.newValue === null){
            logout()
        }
    })
    getStatus()
    // 载入icon资源
    loadIconfont()
})
</script>
<style>
/* 全局样式 */
#app {
    font-family:
        Inter, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial,
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
    margin: 0;
    padding: 0;
    height: 100vh;
}

/* 其他全局样式 */
</style>
