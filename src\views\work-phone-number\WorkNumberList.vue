<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7;">
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <div class="all-padding-16">
                <SearchBox
                    :searchOptionKey="searchOptionKey"
                    @updateSearchParams="updateSearchParams"
                    :customConfig="searchConfig"
                >
                </SearchBox>
            </div>
        </div>
        <div
            ref="tableContentRef"
            class="l-padding-16 t-padding-16 r-padding-16 table-content"
            style="background-color: #fff; box-sizing: border-box"
        >
            <div class="back-color-white border-box flex-grow-1">
                <el-table
                    ref="tableListRef"
                    :data="tableData"
                    v-loading="tableLoading"
                    row-key="id"
                    header-row-class-name="tableHeader"
                    :style="{ 'min-height': tableHeight + 'px' }"
                >
                    <template v-if="!tableLoading" #empty>
                        <div class="display-flex flex-column top-bottom-center">
                            <img class="w-260 h-260" src="@/assets/images/customer-public-no-data.png" alt="暂无数据" />
                            <div class="font-first-title-unactive color-two-grey b-margin-16">暂无数据</div>
                        </div>
                    </template>
                    <el-table-column label="办税小号" prop="telX"></el-table-column>
                    <el-table-column label="绑定状态" prop="authStatus">
                        <template #default="scope">
                            <el-tag type="primary" text bg v-if="scope.row.authStatus === '0'">未绑定</el-tag>
                            <el-tag type="info" text bg v-if="scope.row.authStatus === '1'">实名认证中</el-tag>
                            <el-tag type="success" text bg v-if="scope.row.authStatus === '2'">实名认证成功</el-tag>
                            <el-tag type="danger" text bg v-if="scope.row.authStatus === '3'">超过7天未实名成功</el-tag>
                            <el-tag type="info" text bg v-if="scope.row.authStatus === '4'">待解绑</el-tag>
                            <el-tag type="danger" text bg v-if="scope.row.authStatus === '5'">解绑失败</el-tag>
                            <el-tag type="success" text bg v-if="scope.row.authStatus === '6'">解绑成功</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="办税员号码" prop="telB"></el-table-column>
                    <el-table-column v-if="isAdmin" label="所属租户" prop="tenantName"></el-table-column>
                    <el-table-column label="操作" >
                        <template #default="scope">
                            <div class="display-flex gap-12 top-bottom-center">
                                <div
                                    v-if="scope.row.authStatus === '0' || scope.row.authStatus === '6'"
                                    @click="bindPhone(scope.row)"
                                    class="color-primary pointer"
                                >
                                    绑定办税员
                                </div>
                                <div
                                    v-if="scope.row.authStatus !== '0' && scope.row.authStatus !== '6' " 
                                    class="color-red pointer"
                                    @click="unbindPhone(scope.row)"
                                >
                                    解绑办税员
                                </div>
                                <div
                                    v-if="scope.row.authStatus !== '0' && scope.row.authStatus !== '6'"
                                    class="color-primary pointer"
                                    @click="taxerConfig(scope.row)"
                                >
                                    办税员配置
                                </div>
                                <el-dropdown trigger="click" v-if="scope.row.authStatus !== '0' && scope.row.authStatus !== '6' && !isAdmin">
                                    <div class="el-dropdown-link" style="text-decoration: none" >更多</div>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item>
                                                <div
                                                    class="color-primary pointer"
                                                    @click="addMobile(scope.row)"
                                                >
                                                    添加小号
                                                </div>
                                            </el-dropdown-item>
                                            <el-dropdown-item>
                                                <div
                                                    class="color-primary pointer"
                                                    @click="bindCompany(scope.row)"
                                                >
                                                    绑定企业
                                                </div>
                                            </el-dropdown-item>
                                            <el-dropdown-item>
                                                <div
                                                    class="color-primary pointer"
                                                    @click="confirmTaxer(scope.row)"
                                                >
                                                    办税员确认
                                                </div>
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <el-affix v-show="!tableLoading" target=".table-content" position="bottom" :z-index="2">
                    <div class="pagination-bar">
                        <el-pagination
                            v-model:currentPage="pageInfo.page"
                            v-model:page-size="pageInfo.size"
                            :total="pageInfo.total"
                            :page-sizes="[20, 40, 60, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            @change="pageChange"
                        />
                    </div>
                </el-affix>
            </div>
        </div>
    </div>
    <!-- 绑定办税员弹窗 -->
    <el-dialog v-model="bindVisible" title="绑定办税小号" @close="handleBindClose" width="500px">
        <div class="display-flex flex-column gap-8">
            <div class="display-flex flex-column gap-8">
                <div class="display-flex top-bottom-center gap-12">
                    <div class="color-red">*</div>
                    <div>办税小号</div>
                </div>
                <el-input v-model="bindWorkNumber" disabled></el-input>
            </div>
            <div class="display-flex flex-column gap-8">
                <div class="display-flex top-bottom-center gap-12">
                    <div class="color-red">*</div>
                    <div>办税员手机号</div>
                </div>
                <el-input v-model="bindTaxerPhone" @input="validatePhone"></el-input>
                <div v-if="!phoneValid || bindTaxerPhone === ''" class="color-red font-12">{{ errMsg }}</div>
            </div>
            <div class="t-margin-16 display-flex flex-end">
                <el-button class="l-margin-12 all-padding-16" @click="handleBindClose">取消</el-button>
                <el-button 
                    :loading="confirmLoading" 
                    type="primary" 
                    class="all-padding-16" 
                    @click="submitBind"  
                >提交</el-button
                >
            </div>
        </div>
    </el-dialog>
    <!-- 办税员配置弹窗 -->
    <el-dialog
        v-model="taxerConfigVisible"
        title="办税员配置"
        @close="resetFormdata(ruleFormRef)"
        width="620px"
    >
        <div class="display-flex flex-column gap-8">
            <el-form
                ref="ruleFormRef"
                :inline="true"
                :model="formData" 
                class="demo-form-inline"
                :rules="rules"
            >
                <el-form-item>
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税小号</span>
                        </div>
                        <el-input v-model="formData.workNumber" disabled></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="name">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员姓名</span>
                        </div>
                        <el-input v-model="formData.name" clearable></el-input>
                    </div>
                </el-form-item>
                <el-form-item>
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员手机号</span>
                        </div>
                        <el-input v-model="formData.phoneNumber" disabled></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="idCard">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员身份证号</span>
                        </div>
                        <el-input v-model="formData.idCard" clearable></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="loginCode">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">办税员密码</span>
                        </div>
                        <el-input v-model="formData.loginCode" type="password" show-password clearable></el-input>
                    </div>
                </el-form-item>
                <el-form-item prop="sflx">
                    <div class="display-flex flex-column gap-4">
                        <div class="display-flex top-bottom-center gap-4">
                            <span class="color-red">*</span>
                            <span class="">身份类型</span>
                        </div>
                        <el-select v-model="formData.sflx" clearable>
                            <el-option label="办税员" value="BSY"></el-option>
                            <!-- <el-option label="开票员" value="KPY"></el-option> -->
                            <!-- <el-option label="财务负责人" value="CWFZR"></el-option> -->
                        </el-select>
                    </div>
                </el-form-item>
            </el-form>
            <div class="t-margin-16 display-flex flex-end">
                <el-button class="l-margin-12 all-padding-16" @click="resetFormdata(ruleFormRef)">取消</el-button>
                <el-button 
                    :loading="confirmLoading" 
                    type="primary" 
                    class="all-padding-16" 
                    @click="submitTaxerConfig(ruleFormRef)"  
                >提交</el-button
                >
            </div>
        </div>
    </el-dialog>
    <!-- 绑定企业弹窗 -->
    <!-- <BindCompanyList :visible="bindCompanyVisible" @update:visible="handleBindCompanyClose" :workNumberInfo="workNumberInfo" :channelId="channelId" /> -->
    <el-dialog
        title="绑定企业"
        v-model="addDialogVisible"
        width="500px"
    >
        <div class="display-flex flex-column gap-8">
            <div class="display-flex flex-column gap-8">
                <div class="display-flex gap-4">
                    <span class="color-red">*</span>
                    <span class="">企业名称</span>
                </div>
                <el-select 
                    v-model="nsrsbh"
                    filterable
                    remote
                    reserve-keyword
                    :loading="loading"
                    :remote-method="remoteMethod"
                    placeholder="请输入企业名称"
                >
                    <el-option
                        v-for="item in dataList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </div>
            <div class="display-flex flex-column gap-8">
                <div class="display-flex gap-4">
                    <span class="color-red">*</span>
                    <span class="">企业税号</span>
                </div>
                <el-input v-model="nsrsbh" disabled></el-input>
            </div>
            <div class="t-margin-16 display-flex flex-end">
                <el-button class="l-margin-12 all-padding-16" @click="handleAddClose">取消</el-button>
                <el-button 
                    :loading="confirmLoading" 
                    type="primary" 
                    class="all-padding-16" 
                    @click="submitBondCompany"  
                >提交</el-button
                >
            </div>
        </div>
    </el-dialog>
    <!-- 添加办税员链接 -->
    <el-dialog
        title="绑定企业"
        v-model="showAddTaxerDialog"
        width="500px"
        @close="handleAddTaxerClose"
    >
        <div>
            <div class="ewmstyle t-margin-12 flex-center flex-column border-radius-8 border gap-8 all-padding-16" >
                <div class="font-14 color-black">请扫码后用
                    <span class="color-red">法人/财务负责人</span>
                    身份登录
                </div>
                <div class="qr-container" v-loading="ewmLoading">
                    <div class="scan-frame">
                        <div class="scan-corner scan-corner-tl"></div>
                        <div class="scan-corner scan-corner-tr"></div>
                        <div class="scan-corner scan-corner-bl"></div>
                        <div class="scan-corner scan-corner-br"></div>
                        <vue-qr
                            class="qr-code"
                            ref="qrcode"
                            :title="addTaxerUrl"
                            :text="addTaxerUrl"
                            :size="120"
                            :margin="0"
                        ></vue-qr>
                    </div>
                </div>
                <span>失效时间：{{ endTime || '' }}</span>
                <el-button type="primary" @click="copyUrl(addTaxerUrl)">复制链接发送给客户</el-button>
            </div>
        </div>
    </el-dialog>
    <!-- 办税员确认链接 -->
    <el-dialog
        title="办税员确认"
        v-model="showConfirmTaxerDialog"
        width="500px"
        @close="showConfirmTaxerClose"
    >
        <div class="ewmstyle t-margin-12 flex-center flex-column border-radius-8 border gap-8 all-padding-16" >
            <div class="font-14 color-black">
                请用
                <span class="color-red">自然人身份</span>
                登录电子税务局APP
            </div>
            <div class="qr-container" v-loading="ewmLoading">
                <div class="scan-frame">
                    <div class="scan-corner scan-corner-tl"></div>
                    <div class="scan-corner scan-corner-tr"></div>
                    <div class="scan-corner scan-corner-bl"></div>
                    <div class="scan-corner scan-corner-br"></div>
                    <vue-qr
                        class="qr-code"
                        ref="qrcode"
                        :title="comfirmUrl"
                        :text="comfirmUrl"
                        :size="120"
                        :margin="0"
                    ></vue-qr>
                </div>
            </div>
            <span>失效时间：{{ endTime || '' }}</span>
            <el-button type="primary" @click="copyUrl(comfirmUrl)">复制链接发送给客户</el-button>
        </div>
    </el-dialog>
    <el-dialog v-model="addMobileVisible" width="700px" v-if="addMobileVisible" @closed ="handleClose ">
        <template #header="{ titleId, titleClass }">
            <div class="display-flex space-between padding-top-5 top-bottom-center tb-margin-0">
                <div :id="titleId" :class="titleClass">添加手机号码</div>
                <div class="margin-right-36 font-size-14 pointer">
                    <el-popover :visible="showQrCode" placement="top" :width="220">
                        <div>
                            <vue-qr
                                class="border t-margin-8"
                                ref="qrcode"
                                :title="addMobileUrl"
                                :text="addMobileUrl"
                                :size="180"
                            ></vue-qr>
                        </div>
                        <div class="margin-top-16">
                            <el-button type="primary" @click="copyUrl(addMobileUrl)">复制链接发送给客户填写</el-button>
                        </div>
                        <template #reference>
                            <div @click="showScanCode" class="display-flex top-bottom-center pointer gap-10">
                                <el-icon>
                                    <FullScreen />
                                </el-icon>
                                面对面扫码
                            </div>
                        </template>
                    </el-popover>
                </div>
            </div>
        </template>

        <iframe id="myIframe" class="width-100" style="height: 100vh" :src="addMobileUrl" frameborder="0"></iframe>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useFixedActionBar } from '@/js/use-fixed-action-bar'
import { useStore } from 'vuex'
import systemService from '@/service/systemService'
import type { IWorkNumberListParams, IWorkNumberListTableDataItem, IBindWorkNumberParams, IBindTaxOfficerParams, IWorkCompanyAuthParams } from '@/types/worknumber'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
// import BindCompanyList from './components/BindCompanyDialog.vue'
import tamService from '@/service/tamService'
import crmService from '@/service/crmService'
import vueQr from 'vue-qr/src/packages/vue-qr.vue'

type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const searchConfig = ref<CustomConfig>()
const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})

const tableContentRef = ref<HTMLDivElement | null>(null)
useFixedActionBar(tableContentRef)
const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            actionBarContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}

const pageInfo = reactive({
    page: 1,
    size: 20,
    total: 0,
})

const tableLoading = ref(false)
const tableData = ref<IWorkNumberListTableDataItem[]>([])
const queryParams = ref<IWorkNumberListParams>({
    page: 1,
    size: 20,
})
const updateSearchParams = (params: IWorkNumberListParams) => {
    queryParams.value = params
    search()
}
const pageChange = (currentPage: number, size: number) => {
    queryParams.value.page = currentPage
    queryParams.value.size = size
    search()
}

const search = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.size = pageInfo.size
    // console.log('queryParams', queryParams.value)
    tamService.workNumberList(queryParams.value).then((response) => {
        // console.log('search', response)
        tableData.value = response.records
        pageInfo.total = response.total
    }).finally(() => {
        tableLoading.value = false
    })
}
const confirmLoading = ref(false)

// 绑定相关
const bindVisible = ref(false)
const bindWorkNumber = ref('')
const bindTaxerPhone = ref('')
const phoneValid = ref(true)
const bindWorkNumberParams = ref<IBindWorkNumberParams>({
    bindType: 1,
    telX: '',
    telB: '',
})
const errMsg = computed(() => {
    if (!phoneValid.value) {
        return '请输入正确的手机号码'
    }
    if (bindTaxerPhone.value === '') {
        return '请输入办税员手机号'
    }
    return ''
})
const validatePhone = () => {
    const phoneRegex = /^1[3-9]\d{9}$/
    phoneValid.value = phoneRegex.test(bindTaxerPhone.value)
}
const bindPhone = (row: IWorkNumberListTableDataItem) => {
    // console.log('bindPhone', row)
    bindVisible.value = true
    bindWorkNumber.value = row.telX
    bindWorkNumberParams.value.telX = row.telX
}
const handleBindClose = () => {
    bindTaxerPhone.value = ''
    bindVisible.value = false
    confirmLoading.value = false
}
const submitBind = () => {
    if (!phoneValid.value || bindTaxerPhone.value === '') return
    confirmLoading.value = true
    bindWorkNumberParams.value.telB = bindTaxerPhone.value
    tamService.workNumberBind(bindWorkNumberParams.value).then((response) => {
        // console.log('submitBind', response)
        if(response.success){
            ElMessage.success('绑定成功')
            bindVisible.value = false
            search()
        }else{
            ElMessage.error(response.errMsg)
        }
    }).finally(() => {
        confirmLoading.value = false
    })
}

// 解绑相关
const unbindPhone = (row: IWorkNumberListTableDataItem) => {
  
    const unbindWorkNumberParams : IBindWorkNumberParams = {
        bindType: 2,
        telX: row.telX,
        telB:row.telB
    }
    // console.log('unbindPhone', unbindWorkNumberParams)
    ElMessageBox.confirm('是否确认解绑?',
        '提示',
        {
            confirmButtonText: '解绑',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {
        tamService.workNumberBind(unbindWorkNumberParams).then((response) => {
            // console.log('submitUnbind', response)
            if(response.success){
                ElMessage.success('解绑成功')
                search()
            }else{
                ElMessage.error(response.errMsg)
            }
        })
    })
}

// 办税员配置相关
const taxerConfigVisible = ref(false)
const taxerConfig = (row: IWorkNumberListTableDataItem) => {
    // console.log('taxerConfig', row)
    taxerConfigVisible.value = true
    formData.phoneNumber = row.telB
    formData.workNumber = row.telX
    tamService.getTaxOfficerInfo({telX:row.telX,channelId:row.channelId}).then((response) => {
        console.log('getTaxOfficerInfo', response)
        if(response.success){
            formData.name = response.data.bsyxm
            formData.idCard = response.data.bsysfzhm
            formData.loginCode = response.data.bsymm
            // console.log('接口返回的sflX:', response.data.sflx)
            formData.sflx = 'BSY'
            // console.log('赋值后的formData.sflx:', formData.sflx)
        }
    })
}

const formData = reactive({
    workNumber:'',
    name:'',
    phoneNumber:'',
    idCard:'',
    loginCode:'',
    sflx:'',
})

const resetFormdata = (formEl: FormInstance | undefined) => {
    console.log('formEl',formEl)
    if (formEl) {
        formEl.resetFields()
    }
    clearFormData()
    handleTaxerConfigClose()
}
const clearFormData = () => {
    if (ruleFormRef.value) {
        ruleFormRef.value.clearValidate()
    }
    Object.assign(formData, {
        workNumber: '',
        name: '',
        phoneNumber: '',
        idCard: '',
        loginCode: '',
        sflx: '',
    })
}
const handleTaxerConfigClose = () => {
    taxerConfigVisible.value = false
}
interface RuleForm {
  workNumber: string
  name: string
  phoneNumber: string
  idCard: string
  loginCode: string
  sflx: boolean
}
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules<RuleForm>>({
    name: [{required: true, message: '请输入办税员姓名', trigger: 'blur'}],
    idCard: [
        {required: true, message: '请输入办税员身份证号', trigger: 'blur'},
        {}
    ],
    loginCode: [{required: true, message: '请输入办税员密码', trigger: 'blur'}],
    sflx: [{required: true, message: '请选择办税员身份', trigger: 'blur'}],
})
const bindTaxOfficerParams = ref<IBindTaxOfficerParams>({
    bsyxm:'',
    bsysjhm:'',
    sflX:'',
    bsysfzhm:'',
    bsymm:'',
    telX:''
})
const submitTaxerConfig = async (formEl: FormInstance | undefined) => {
    confirmLoading.value = true
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            // console.log('提交表单数据',formData)
            bindTaxOfficerParams.value.bsyxm = formData.name
            bindTaxOfficerParams.value.bsysjhm = formData.phoneNumber
            bindTaxOfficerParams.value.sflX = formData.sflx
            bindTaxOfficerParams.value.bsysfzhm = formData.idCard
            bindTaxOfficerParams.value.bsymm = formData.loginCode
            bindTaxOfficerParams.value.telX = formData.workNumber
            // console.log('bindTaxOfficerParams', bindTaxOfficerParams.value)
            tamService.taxOfficerConfig(bindTaxOfficerParams.value).then((response) => {
                // console.log('submitTaxerConfig', response)
                if(response.success){
                    ElMessage.success('操作成功')
                    clearFormData()
                    handleTaxerConfigClose()
                }else{
                    ElMessage.error(response.errMsg)
                }
            }).finally(() => {
                confirmLoading.value = false
            })
        } else {
            console.log('error submit!', fields)
        }
    })
}

// 添加手机号相关
const addMobileVisible = ref(false)
const addMobileUrl = ref('')
const showQrCode = ref<boolean>(false)
const addMobile = (row: IWorkNumberListTableDataItem) => {
    console.log('addMobile', row)
    tamService.getTaxOfficerInfo({telX:row.telX,channelId:row.channelId}).then((response) => {
        if(response.success){
            if(response.data.bsysjhm){
                tamService.addMobile({bsysjhm:response.data.bsysjhm,channelId:row.channelId}).then((res) => {
                    console.log('res',res)
                    addMobileUrl.value = res.authUrl
                    addMobileVisible.value = true
                })
            }else{
                ElMessage.error('请先配置办税员信息')
            }
        }else{
            ElMessage.error(response.errMsg)
        }
    })
}
const showScanCode = () => {
    showQrCode.value = !showQrCode.value
}

const handleClose = () => {
    addMobileVisible.value = false
    showQrCode.value = false
}

// 绑定企业相关 
const addDialogVisible = ref(false)
const ewmLoading = ref(false)
const showAddTaxerDialog = ref(false)
const addTaxerUrl = ref('')
const endTime = ref('')
const workNumberInfo = ref<IWorkNumberListTableDataItem>()
const channelId = ref('')
const bsySfz = ref('')
const bsyName = ref('')
const bindCompany = (row: IWorkNumberListTableDataItem) => {
    tamService.getTaxOfficerInfo({telX:row.telX,channelId:row.channelId}).then((response) => {
        console.log('getTaxOfficerInfo', response)
        if(response.success){
            if(response.data.bsyxm){
                bsySfz.value = response.data.bsysfzhm
                bsyName.value = response.data.bsyxm
                channelId.value = row.channelId
                // console.log('bindCompany', row)
                workNumberInfo.value = row
                addDialogVisible.value = true
            }else{
                ElMessage.error('请先配置办税员信息')
            }
        }else{
            ElMessage.error(response.errMsg)
        }
    }) 
}
const nsrsbh = ref('')
const companyName = ref('')
interface ListItem {
  value: string
  label: string
}
const dataList = ref<ListItem[]>([])
watch(nsrsbh, (newValue) => {
    const selectedItem = dataList.value.find(item => item.value === newValue)
    if (selectedItem) {
        companyName.value = selectedItem.label
    }
})
const loading = ref(false)
const remoteMethod =async (query: string) => {
    if (query) {
        loading.value = true
        const list = await crmService.getCrmList({page: 1, pageSize: 20, companyName: query})
        if(list.data && list.data.length > 0){
            dataList.value = list.data.map((item) => {
                return {
                    value: item.socialCreditCode,
                    label: item.companyName,
                }
            })
        }
        loading.value = false
    } else {
        dataList.value = []
    }
}
const submitBondCompany =async () => {
    console.log('props.channelId',channelId.value)
    console.log('props.workNumberInfo',workNumberInfo)
    if(!companyName.value) {
        ElMessage.error('请输入企业名称')
        return
    }
    if(workNumberInfo.value){
        addTaxer(workNumberInfo.value)
    }
}

const addTaxer = (row:IWorkNumberListTableDataItem) => {
    ewmLoading.value = true
    console.log(row)
    showAddTaxerDialog.value = true

    // 设置失效时间为当前时间加24小时
    const now = new Date()
    const expireTime = new Date(now.getTime() + 24 * 60 * 60 * 1000)
    endTime.value = expireTime.toLocaleString('zh-CN')

    const params : IWorkCompanyAuthParams = {
        nsrsbh:nsrsbh.value,
        qymc:companyName.value,
        bsysfzhm:bsySfz.value,
        bsyxm:bsyName.value,
        bsysjhm:row.telB,
        sflx:'BSY',
        tenantId:row.tenantId,
    }
    tamService.companyAuth(params).then((res) => {
        console.log('res',res)
        addTaxerUrl.value = res.authUrl
        ewmLoading.value = false
    })
}

// 办税员确认
const showConfirmTaxerDialog = ref(false)
const comfirmUrl = ref('')
const confirmTaxer = (row:IWorkNumberListTableDataItem) => {
    ewmLoading.value = true
    tamService.getTaxOfficerInfo({telX:row.telX,channelId:row.channelId}).then((response) => {
        console.log('getTaxOfficerInfo', response)
        if(response.success){
            if(response.data.bsyxm){
                showConfirmTaxerDialog.value = true
            }else{
                ElMessage.error('请先配置办税员信息')
                return
            }
        }else{
            ElMessage.error(response.errMsg)
            return 
        }
    }) 
    // 设置失效时间为当前时间加24小时
    const now = new Date()
    const expireTime = new Date(now.getTime() + 24 * 60 * 60 * 1000)
    endTime.value = expireTime.toLocaleString('zh-CN')

    const params = {
        bsysjhm:row.telB,
    }
    tamService.bsyConfirm(params).then((res) => {
        console.log('res',res)
        comfirmUrl.value = res.authUrl
        ewmLoading.value = false
    })
}

const showConfirmTaxerClose = () => {
    showConfirmTaxerDialog.value = false
}

const handleAddClose = () => {
    addDialogVisible.value = false
}
const handleAddTaxerClose = () => {
    showAddTaxerDialog.value = false
}

const copyUrl = (url: string) => {
    const textArea = document.createElement('textarea')

    // 设置内容并添加到文档中
    textArea.value = url.trim()
    document.body.appendChild(textArea)

    // 选中并拷贝内容
    textArea.select()
    document.execCommand('copy')

    // 删除临时的 textarea
    document.body.removeChild(textArea)

    // 可选: 提示用户内容已被复制
    ElMessage({
        message: '复制成功',
        type: 'success',
    })
}

const searchOptionKey = ref('WORK_PHONE_SEARCH_OPTIONS')
onMounted(() => {
    getTableHeight()
    search()
    if (isAdmin.value) {
        searchOptionKey.value = 'WORK_PHONE_SEARCH_OPTIONS_FORADMIN'
        systemService.tenantList().then((response) => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantIds: response.map((item) => ({
                    label: item.name,
                    value: item.id,
                })),
            }
        })
    }
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}

.demo-form-inline .el-input {
  --el-input-width: 250px;
}

.demo-form-inline .el-select {
  --el-select-width: 250px;
}

.ewmstyle{
    border: 1px solid #DEDEDF;
    background: linear-gradient(to bottom, #EDF3FF, #FFFFFF);
}

.qr-container {
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.scan-frame {
    position: relative;
    display: inline-block;
    padding: 20px;
}

.scan-corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #1890ff;
}

.scan-corner-tl {
    top: 0;
    left: 0;
    border-right: none;
    border-bottom: none;
}

.scan-corner-tr {
    top: 0;
    right: 0;
    border-left: none;
    border-bottom: none;
}

.scan-corner-bl {
    bottom: 0;
    left: 0;
    border-right: none;
    border-top: none;
}

.scan-corner-br {
    bottom: 0;
    right: 0;
    border-left: none;
    border-top: none;
}

.qr-code {
    display: block;
}

.el-dropdown-link {
    display: block;
    cursor: pointer;
    color: var(--main-blue-);
    font-size: 16px;
}
</style>
