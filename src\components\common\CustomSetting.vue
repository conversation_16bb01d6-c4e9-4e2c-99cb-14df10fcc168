<script lang="ts" setup>
import type { ISearchItemType, ILeadColumn } from '@/types/lead'
import Icon from '@/components/common/Icon.vue'
import { onMounted, ref, watch } from 'vue'
import { getItem, setItem } from '@/utils/storage'
import type { TreeNode } from 'element-plus'
import * as allTableColumns from '@/js/table-options'
import * as allSearchOptions from '@/js/search-options'

const props = defineProps<{
    visible: boolean
    templateName: string
    from: string,
    tabType: string,
}>()
let title = ref('')
const Columns=ref<ISearchItemType[] | ILeadColumn[]>([])

watch(() => props.from, (newVal) => {
    if (newVal === 'search') {
        title.value = '搜索设置'
    } else {
        title.value = '表格设置'
    }
})
watch(() => props.templateName, (newVal) => {
    if (newVal) {
        if (props.from === 'search') {
            Columns.value = JSON.parse(JSON.stringify(allSearchOptions[newVal as keyof typeof allSearchOptions])) // 使用类型断言确保 newVal 是 allSearchOptions 的键
        } else {
            Columns.value = JSON.parse(JSON.stringify(allTableColumns[newVal as keyof typeof allTableColumns])) // 使用类型断言确保 newVal 是 allTableColumns 的键
        }

    }
}, {
    immediate: true
})
const init = () => {
    let localConfig = getItem(props.templateName)
    if (localConfig) {
        Columns.value = localConfig
    }
}
onMounted(() => {
    init()
})

const filterColumns = (columns: (ISearchItemType | ILeadColumn)[]) => {
    // 【搜索配置】在我的协作线索、我的线索/客户 过滤掉【负责人】
    if(props.tabType !== 'all' && (props.templateName === 'LEAD_SEARCH_OPTIONS' || props.templateName === 'CUSTOMER_SEARCH_OPTIONS')) {
        return columns.filter(item => item.key !== 'user')
    }
    return columns
}

const allowDrop = (draggingNode: TreeNode, dropNode: TreeNode, type: 'before' | 'after' | 'inner') => {
    console.log('是否可拖动', draggingNode, dropNode, type)
    //是否可拖动
    if (type === 'inner') {
        return false
    } else {
        return true
    }
}

const searchQuery = ref<string | null>(null)
const filteredOptions = ref<(ISearchItemType | ILeadColumn)[]>([])
watch(searchQuery, (newVal) => {
    if (newVal) {
        filteredOptions.value = Columns.value.filter((item) =>
            item.label.toLowerCase().includes(newVal.toLowerCase())
        )
    }
})

const emit = defineEmits(['refreshOptions'])
const changeSwitch = () => {
    setItem(props.templateName, Columns.value)
    emit('refreshOptions')
}
const handleEnd = () => {
    setItem(props.templateName, Columns.value)
    emit('refreshOptions')
}
</script>
<template>
    <el-popover ref="popoverContent" :title="title" width="300" placement="right-start" trigger="click">
        <template #reference>
            <slot name="content"></slot>
        </template>
        <el-input placeholder="搜索字段" v-model.trim="searchQuery" clearable></el-input>
        <div class="notice">拖拽列名称，进行排序{{ tabType }}</div>
        <div class="column-setting">
            <div class="column">
                <div class="body">
                    <el-tree
                        class="table-setting"
                        :data="searchQuery ? filteredOptions : filterColumns(Columns)"
                        node-key="key"
                        group="people"
                        @end="handleEnd"
                        draggable
                        @node-drag-end="handleEnd"
                        ghost-class="ghost"
                        style="height: 100%"
                        :allow-drop="allowDrop"
                    >
                        <template #default="{ data }">
                            <div class="line">
                                <view class="display-flex top-bottom-center">
                                    <Icon icon="icon-a-huaban267" style="margin-right: 4px"></Icon>
                                    <span>{{ data.title || data.label }}</span>
                                </view>
                                <el-switch
                                    v-model="data.isShow"
                                    :disabled="!data.editable"
                                    @change="changeSwitch"
                                    size="small"
                                    style="--el-switch-on-color: var(--main-blue-)"
                                />
                            </div>
                        </template>
                    </el-tree>
                </div>
            </div>
        </div>
    </el-popover>
</template>
<style scoped lang="scss">
.line {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 26px;
    line-height: 26px;
    // border: 1px solid $colorH;
    // border-radius: 4px;
    padding: 0 10px 0 10px;
    margin: 2px;
    cursor: pointer;
    background-color: #ffffff;
}
.line:hover {
    opacity: 0.5;
    background: #999999;
    color: #ffffff;
}

.check-item {
    margin-left: 10px;
}
.title {
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: 500;
}
.ghost {
    opacity: 0.5;
    background: #276ef9;
    color: #ffffff;
}
.notice {
    font-size: 12px;
    color: #999999;
    padding: 10px 10px 0 10px;
}
.table-setting {
    max-height: 330px !important;
    overflow: auto;
    :deep(.el-tree-node__expand-icon.is-leaf) {
        display: none;
    }
    .line:hover {
        background-color: #f0f0f0;
        color: #303133;
    }
}
</style>
