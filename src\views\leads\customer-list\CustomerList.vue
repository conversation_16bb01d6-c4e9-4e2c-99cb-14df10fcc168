<script lang="ts" setup>
import { onBeforeMount, onMounted, onBeforeUnmount, reactive, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getItem } from '@/utils/storage'
import eventBus from '@/utils/eventBus'

import { CUSTOMER_SEARCH_OPTIONS } from '@/js/search-options'
import { CUSTOMER_LIST_TABLE_COLUMNS } from '@/js/table-options'

import { ElMessage, ElMessageBox } from 'element-plus'
import Icon from '@/components/common/Icon.vue'
import SearchBox from '@/components/common/SearchBox.vue'
import CustomSetting from '@/components/common/CustomSetting.vue'
import AddLeadDialog from '@/views/leads/components/AddLeadDialog.vue'
import MinistrantDialog from '@/views/leads/customer-list/components/MinistrantDialog.vue'
import ContactListDialog from '@/views/leads/components/ContactListDialog.vue'
import ReportListDialog from '@/views/leads/components/ReportListDialog.vue'
import CrmDetailDrawer from '@/components/crm/CrmDetailDrawer.vue'
import TransferDialog from '@/views/leads/components/TransferDialog.vue'
import ActivitiesDetailDialog from '@/views/leads/components/ActivitiesDetailDialog.vue'
import ExportLead from '@/views/leads/components/ExportLead.vue'
import SendSmsDialog from '@/views/leads/components/SendSmsDialog.vue'
import BatchUpdateContact from '@/views/leads/components/BatchUpdateContact.vue'
import ImportLead from '@/views/leads/components/ImportLead.vue'
import TagPopover from '@/components/tag/TagPopover.vue'

import type { ILeadColumn } from '@/types/lead'
import type { IGetCrmLeadParams, ILeadData, IGetTabPool, IRiskListItem } from '@/types/lead'
import type { TableInstance } from 'element-plus'
import type { IAicConditionDataOptionItem } from '@/types/aic'

import customService from '@/service/customService'
import crmService from '@/service/crmService'
import aicService from '@/service/aicService'
import systemService from '@/service/systemService'
import { parseTime } from '@/utils/parse-time'
import AIChatBox, { type IAIChatBox } from '@/utils/ai'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import permissionService from '@/service/permissionService'
import dataScopeService from '@/service/dataScopeService'
import { useFixedActionBar } from '@/js/use-fixed-action-bar'
import RiskDialog from '@/views/risk-management/risk-alerts/components/RiskDialog.vue'
import AddTagDialog from '@/views/leads/components/AddTagDialog.vue'
const tableContentRef=ref<HTMLDivElement | null>(null)
useFixedActionBar(tableContentRef)

const store = useStore<RootState>()
const aiChat = ref<IAIChatBox>()
const route = useRoute()
const poolList = ref<IGetTabPool[]>([
    {
        name: '全部客户',
        id: 'all',
        sort: 1,
        user: [],
    },
    {
        name: '我的客户',
        id: 'mine',
        sort: 2,
        user: [],
    },
    {
        name: '我协作的客户',
        id: 'with',
        sort: 3,
        user: [],
    },
])
const hasDropdownItems = computed(() => {
    return activeName.value !== 'with' || permissionService.isContactUpdatePermitted()
})
const activeName = ref('mine')
const handleTabChange = () => {
    queryParams = {
        page: 1,
        pageSize: 20,
    }
    clearAllSelected()
    search()
}
const search = async () => {
    tableLoading.value = true
    if (activeName.value === 'mine') {
        queryParams.searchTag = 2 // 查我的客户
    } else if (activeName.value === 'with') {
        queryParams.searchTag = 3 // 查我协作的客户
    } else {
        if ('searchTag' in queryParams) {
            delete queryParams.searchTag
        }
    }
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    console.log('搜索的请求', queryParams)
    let searchRes = await customService.customList(queryParams)
    console.log('搜索的结果', searchRes)
    if (searchRes.errCode === 0) {
        tableLoading.value = false
        tableData.value = searchRes.data ? searchRes.data : []
        pageInfo.total = searchRes.total
    } else {
        tableLoading.value = false
    }
}
type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const customSearchOptionConifg = ref<CustomConfig>({})
type areaOptionArr = {
    label: string
    value: string
    children: areaOptionArr[] | []
}
const dealDIGui = (arr: IAicConditionDataOptionItem[]): areaOptionArr[] => {
    return arr.map((item) => ({
        label: item ? item.label : '',
        value: item ? item.value : '',
        children: item.children && item.children.length > 0 ? dealDIGui(item.children) : [],
    }))
}
const getSearchOptions = async () => {
    const [GetDataRes, tagRes, userRes, createUserRes, importBatchRes, orgRes] = await Promise.all([
        aicService.conditionGetData({}),
        crmService.crmTagList({ page: 1, pageSize: 100 }),
        systemService.userGetUserByScopeData('clue'),
        systemService.userGetUserByScopeData('collect'),
        crmService.crmImportBatchEnum({ isAll: true, type: 2 }),
        systemService.orgGetOrgByScopeData('client')
    ])
    const transformAreaList = dealDIGui(GetDataRes.area)
    const transformTagList = tagRes.data.map((item) => ({ value: item.id, label: item.tagName }))
    const transformUserList = userRes.data.map((item) => ({ value: item.id, label: item.nickname }))
    const transformCreateUserList = createUserRes.data.map((item) => ({ value: item.id, label: item.nickname }))
    const importBatchList = importBatchRes.data.map((item) => ({ value: item.id, label: item.importDateStr + '期' + ` (${item.successNum}家)` }))
    const orgResList = orgRes.data.map((item) => ({value: item.id, label: item.name}))
    customSearchOptionConifg.value = {
        tagIds: transformTagList,
        areaCode: transformAreaList,
        user: transformUserList,
        beforeUser: transformUserList,
        createUser: transformCreateUserList,
        ministrantInfo: transformUserList,
        importBatchIds: importBatchList,
        orgIds: orgResList
    }
}

onBeforeMount(() => {
    getSearchOptions()
})
let defaultQueryParams = reactive<Record<string, boolean | string | number[] | string[]>>({})
onMounted(() => {
    eventBus.$on('refreshBuystatus', () => {
        search()
    })
    eventBus.$on('refreshCrmData', () => {
        search()
    })
    eventBus.$on('refreshSearchOptions', () => {
        getSearchOptions()
    })
    eventBus.$on('clearAllSelected', () => {
        clearAllSelected()
    })
    refreshTableCloumns()
    getTableHeight()
    if (route.query && JSON.stringify(route.query) !== '{}') {
        console.log('route.query', route.query)
        for (const key in route.query) {
            defaultQueryParams = {
                ...defaultQueryParams,
                [key]: route.query[key] as boolean | string | number[] | string[],
            }
        }
    }else{
        search()
    }
    if (dataScopeService.customerScopeData() === 1) {
        poolList.value = [
            {
                name: '我的客户',
                id: 'mine',
                sort: 2,
                user: [],
            },
            {
                name: '我协作的客户',
                id: 'with',
                sort: 3,
                user: [],
            }
        ]
    }
    aiChat.value = new AIChatBox()
})
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            actionBarContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}
//---------------table-----------------//
const settingVisible = ref(false)
const tableHeight = ref(500)
const tableData = ref<ILeadData[]>([])
const tableAllOptions = ref<ILeadColumn[]>(CUSTOMER_LIST_TABLE_COLUMNS)
const refreshTableCloumns = () => {
    const savedTableConfig = getItem('CUSTOMER_LIST_TABLE_COLUMNS')
    if (savedTableConfig) {
        tableAllOptions.value = savedTableConfig
    } else {
        tableAllOptions.value = JSON.parse(JSON.stringify(CUSTOMER_LIST_TABLE_COLUMNS))
    }
    tableAllOptions.value.unshift({
        id: 1,
        type: 'default',
        label: '企业名称',
        key: 'companyName',
        prop: 'companyName',
        width: '180',
        editable: true,
        fixed: 'left',
        isShow: true,
    })
    tableAllOptions.value.push({
        id: 99,
        type: 'default',
        label: '操作',
        key: 'action',
        prop: 'action',
        width: '150',
        fixed: 'right',
        editable: false,
        isShow: true,
    })
}

const updateSearchParams = (params: IGetCrmLeadParams) => {
    queryParams = params
    clearAllSelected()
    search()
}

const riskType = (val:string) => {
    if(val.includes('异常')){
        return 'danger'
    }else if(val.includes('高风险')){
        return 'danger'
    }else if(val.includes('处罚')){
        return 'danger'
    }else{
        return 'info'
    }
}

const riskDialogVisible = ref(false)
const targetCompant=ref<IRiskListItem | null>(null)
const showRisk = (row: IRiskListItem) => {
    targetCompant.value = row
    // targetCompant.value.socialCreditCode = '91320191MA1ML4CL25'
    riskDialogVisible.value=true
}

const riskLevel2Info = (label?: number) => {
    // ["primary", "success", "info", "warning", "danger"]
    if (label === 100) {
        return {
            type: 'danger',
            label: '高风险',
        }
    } else if (label === 50) {
        return {
            type: 'warning',
            label: '中风险',
        }
    } else if (label === 1) {
        return {
            type: 'success',
            label: '低风险',
        }
    } else if (label === 0) {
        return {
            type: 'primary',
            label: '无风险',
        }
    } else {
        return {
            type: 'primary',
            label: '无风险',
        }
    }
}

const tableLoading = ref(true)
const tableListRef = ref<TableInstance>()
const selectedData = ref<ILeadData[]>([])
const checkIds = ref<string[]>([])
const handleSelectionChange = (val: ILeadData[]) => {
    if (val.length < 2001) {
        selectedData.value = val
        checkIds.value = val.map((i) => {
            return i.id
        })
    } else {
        let newRow = val.slice(2000)
        console.log('newRow', newRow)
        for (let index = 0; index < newRow.length; index++) {
            tableListRef.value!.toggleRowSelection(newRow[index], false)
        }
    }
}

const handleDel = (val: string[]) => {
    if (val.length > 0) {
        ElMessageBox.confirm('是否确认删除', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(async () => {
            let resRes = await crmService.crmDelete({ ids: val })
            console.log(resRes.success)
            if (resRes.success === true) {
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
                clearAllSelected()
                search()
            }
        })
    } else {
        ElMessage({
            type: 'warning',
            message: '请选择需要删除的客户',
        })
    }
}

// 清空用户的选择
const clearAllSelected = () => {
    if (tableListRef.value) {
        tableListRef.value.clearSelection()
    }
}

const addVisible = ref(false)
const handleAddLead = () => {
    addVisible.value = true
}
const handleRefreshData = () => {
    addVisible.value = false
    search()
}
const handleCloseTransVisible = (type?: string) => {
    // transVisible.value = false
    if (type !== 'cancel') {
        clearAllSelected()
        search()
    }
}
const ministrantVisible = ref(false)
const handleOpenMinistrant = () => {
    if (checkIds.value.length < 1) {
        ElMessage({
            type: 'warning',
            message: '请选择添加协作人的客户',
        })
    } else {
        ministrantVisible.value = true
    }
}
const handleCloseMinistrantDialog = (type?: string) => {
    ministrantVisible.value = false
    if (type !== 'cancel') {
        clearAllSelected()
        search()
    }
}
type formType = {
    id: string
    nickname: string
}
const dealMinistrantInfo = (arr: formType[]) => {
    if (arr.length > 0) {
        return arr
            .map((item) => {
                return item.nickname
            })
            .join('、')
    } else {
        return '-'
    }
}

const contactListVisible = ref(false)
const checkedSCompanyInfo = ref<ILeadData>()
// 打开联系方式
const handleOpenContact = (row: ILeadData) => {
    console.log(row)
    checkedSCompanyInfo.value = row
    contactListVisible.value = true
}

// 查看详情
const crmId = ref('')
// 记录菜单类型
const crmType = ref('list')
const crmDetailDrawerFlag = ref(false)
const handleDetail = (id: string) => {
    crmId.value = id
    crmDetailDrawerFlag.value = true
}

// 发送短信
const smsVisible = ref(false)
const handleSendSms = () => {
    console.log('点击了发送短信')
    if (checkIds.value.length < 1) {
        ElMessage({
            type: 'warning',
            message: '请选择要发送的人',
        })
    } else {
        smsVisible.value = true
    }
}
const handleCloseSmsVisible = (type?: string) => {
    smsVisible.value = false
    if (type !== 'cancel') {
        clearAllSelected()
    }
}

// 查看报告
const reportListVisible = ref(false)
const handleSeeReport = (row: ILeadData) => {
    checkedSCompanyInfo.value = row
    reportListVisible.value = true
}

const startAIChat = (row: ILeadData) => {
    if (!aiChat.value) return
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { id, tenantId } = user || {}
    const { socialCreditCode, companyName } = row
    const key = id + '_' + socialCreditCode
    aiChat.value.setUser(key)
    aiChat.value.showChat()
    aiChat.value.sendMessage({
        message: 'sys.extInitial',
        hide: true,
        inputs: {
            companyName: companyName,
            socialCreditCode: socialCreditCode,
            tenantId: tenantId || '',
        },
    })
}

//---------------pagination-----------------//
let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

let queryParams = reactive<IGetCrmLeadParams>({
    page: 1,
    pageSize: 20,
})
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    search()
}

// 添加标签
const addTagVisible = ref(false)
const handleAddTag = () => {
    if (selectedData.value.length < 1) {
        ElMessage({
            type: 'warning',
            message: '请选择需要添加标签的客户',
        })
    } else {
        addTagVisible.value = true
    }
}
const handleCloseAddTagVisible = (type: string) => {
    addTagVisible.value = false
    if (type !== 'cancel') {
        clearAllSelected()
        search()
    }
}


// 在组件卸载前解绑
onBeforeUnmount(() => {
    eventBus.$off('refreshBuyStatus', () => {})
    eventBus.$off('refreshCrmData', () => {})
    eventBus.$off('refreshSearchOptions', () => {})
})
</script>
<template>
    <div ref="mainContentRef" class="height-100 oa">
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <el-tabs v-model="activeName" @tab-change="handleTabChange()">
                <el-tab-pane v-for="tab in poolList" :key="tab.id" :label="tab.name" :name="tab.id">
                    <SearchBox
                        v-if="activeName === tab.id"
                        :searchOptionKey="'CUSTOMER_SEARCH_OPTIONS'"
                        :customConfig="customSearchOptionConifg"
                        :defaultValue="defaultQueryParams"
                        :tabType="activeName"
                        @updateSearchParams="updateSearchParams"
                    ></SearchBox>
                </el-tab-pane>
            </el-tabs>
        </div>

        <div ref="tableContentRef" class="l-padding-16 t-padding-16 r-padding-16 table-content" style="background-color: #fff; box-sizing: border-box">
            <!-- 工具条 -->
            <div ref="actionBarContentRef" class="display-flex top-bottom-center space-between action-bar">
                <div class="display-flex top-bottom-center">
                    <div class="choose-content">已选{{ selectedData.length }}</div>
                    <!-- 转移 -->
                    <div class="r-margin-16" v-if="activeName !== 'with'">
                        <TransferDialog
                            :from="'customerList'"
                            :showConfig="['customer2Person', 'customer2Pool']"
                            :selectedData="selectedData"
                            @closeVisible="handleCloseTransVisible"
                        />
                    </div>
                    <!-- 导出 -->
                    <div v-if="permissionService.isCompanyExportPermitted()" class="r-margin-16">
                        <ExportLead :from="'customerList'" :checkedIds="checkIds" :queryParams="queryParams" />
                    </div>
                    <!-- 导入 -->
                    <div v-if="permissionService.isCompanyImportPermitted()" class="r-margin-16">
                        <ImportLead :from="'customerList'" @refreshData="handleRefreshData" />
                    </div>
                    <!-- 批量操作 -->
                    <div class="r-margin-16" v-if="hasDropdownItems">
                        <el-dropdown trigger="click">
                            <el-button class="no-focus-visible color-black">
                                批量操作
                                <el-icon class="el-icon--right">
                                    <CaretBottom />
                                </el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item v-if="activeName !== 'with'" @click="handleOpenMinistrant()"
                                    ><span class="color-black">协作人</span></el-dropdown-item
                                    >
                                    <el-dropdown-item v-if="permissionService.isContactUpdatePermitted()">
                                        <!-- 更新联系方式 -->
                                        <BatchUpdateContact :selectedData="selectedData" @clearAllSelected="clearAllSelected"></BatchUpdateContact>
                                    </el-dropdown-item>
                                    <el-dropdown-item v-if="activeName !== 'with' && permissionService.isCompanySendMessagePermitted()" @click="handleSendSms()"
                                    ><span class="color-black">发送短信</span></el-dropdown-item
                                    >
                                    <el-dropdown-item v-if="activeName !== 'with' && permissionService.isCompanyDeletePermitted()" @click="handleDel(checkIds)"
                                    ><span class="color-black">批量删除</span></el-dropdown-item
                                    >
                                    <el-dropdown-item  @click="handleAddTag()"><span class="color-black">添加标签</span></el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
                <div class="display-flex">
                    <div class="r-margin-16" v-if="activeName !== 'with' && permissionService.isCompanyAddPermitted()">
                        <el-button class="color-black" @click="handleAddLead">新增</el-button>
                    </div>
                    <div class="table-setting">
                        <!-- 表格设置弹窗 -->
                        <CustomSetting
                            :visible="settingVisible"
                            :from="'table'"
                            templateName="CUSTOMER_LIST_TABLE_COLUMNS"
                            :tabType="activeName"
                            @refreshOptions="refreshTableCloumns"
                        >
                            <template v-slot:content>
                                <el-button class="r-margin-16" @click="settingVisible = !settingVisible">
                                    <Icon
                                        class="table-setting-icon color-black"
                                        icon="icon-a-xitongguanli"
                                    ></Icon>
                                </el-button>
                            </template>
                        </CustomSetting>
                    </div>
                </div>
            </div>

            <!-- table -->
            <div class="t-margin-16">
                <el-table
                    ref="tableListRef"
                    :data="tableData"
                    v-loading="tableLoading"
                    row-key="id"
                    @selection-change="handleSelectionChange"
                    :style="{ 'min-height': tableHeight + 'px' }"
                >
                    <template v-if="!tableLoading" #empty>
                        <div class="display-flex flex-column top-bottom-center" :style="{'min-height': (tableHeight-40)+'px'}">
                            <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                            <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                        </div>
                    </template>
                    <el-table-column type="selection" width="55" fixed="left" reserve-selection />
                    <el-table-column type="index" width="60" label="序号" fixed="left" />

                    <el-table-column
                        v-for="columns in tableAllOptions.filter((item) => item.isShow === true)"
                        :key="columns.key"
                        :prop="columns.prop"
                        :label="columns.label"
                        :width="columns.width"
                        :type="columns.type"
                        :fixed="columns.fixed"
                        :sortable="columns.sortable"
                        show-overflow-tooltip
                    >
                        <template #default="scope">
                            <template v-if="columns.prop === 'companyName'">
                                <div style="color: var(--main-blue-); cursor: pointer" @click="handleDetail(scope.row.id)">
                                    {{ scope.row.companyName || '-' }}
                                </div>
                            </template>
                            <template v-else-if="columns.prop === 'status'">
                                {{
                                    CUSTOMER_SEARCH_OPTIONS.find((item) => {
                                        return item.key === 'status'
                                    })?.options?.find((i) => {
                                        return i.value === scope.row.status
                                    })?.label || '-'
                                }}
                            </template>
                            <template v-else-if="columns.prop === 'source'">
                                {{
                                    CUSTOMER_SEARCH_OPTIONS.find((item) => {
                                        return item.key === 'source'
                                    })?.options?.find((i) => {
                                        return i.value === scope.row.source
                                    })?.label || '-'
                                }}
                            </template>
                            <template v-else-if="columns.prop === 'riskLevel'">
                                <el-tag
                                    effect="plain"
                                    class="pointer"
                                    :type="riskLevel2Info(scope.row.riskLevel).type"
                                    v-if="scope.row.riskLevel || scope.row.riskLevel === 0"
                                >
                                    {{ riskLevel2Info(scope.row.riskLevel).label }}
                                </el-tag>
                                <el-tag class="ml-2" v-else type="info">暂无风险等级</el-tag>
                            </template>
                            <template v-else-if="columns.prop === 'tagInfos'">
                                <TagPopover
                                    :tagInfos="scope.row.tagInfos"
                                    :type="'list'"
                                    :crmId="scope.row.id"
                                ></TagPopover>
                            </template>
                            <template v-else-if="columns.prop === 'basicScore'">
                                {{ scope.row.basicScore ? scope.row.basicScore.toFixed(0) : '-' }}
                            </template>
                            <template v-else-if="columns.prop === 'contactInfo.mobile'">
                                {{ scope.row.contactInfo.mobile ? scope.row.contactInfo.mobile : '暂无联系方式' }}
                            </template>
                            <template v-else-if="columns.prop === 'aiphoneResult'">
                                <el-tag v-if="scope.row.aiphoneResult === 1" class="pointer">有意向</el-tag>
                                <div v-else-if="scope.row.aiphoneResult === 0">无意向</div>
                                <div v-else>-</div>
                            </template>
                            <template v-else-if="columns.prop === 'ministrantInfos'">
                                {{ (scope.row?.ministrantInfos && dealMinistrantInfo(scope.row.ministrantInfos)) || '-' }}
                            </template>
                            <template v-else-if="columns.prop === 'createTime'">
                                {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
                            </template>
                            <template v-else-if="columns.prop === 'nextFollowDate'">
                                {{ parseTime(scope.row.nextFollowDate, '{y}-{m}-{d} {h}:{i}:{s}')  || '-' }}
                            </template>
                            <template v-else-if="columns.prop == 'newFollowDescription'">
                                <ActivitiesDetailDialog :rowItem="scope.row" />
                            </template>
                            <template v-else-if="columns.prop === 'invoiceCollectDate'">
                                {{ parseTime(scope.row.invoiceCollectDate, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
                            </template>
                            <template v-else-if="columns.prop === 'taxCollectDate'">
                                {{ parseTime(scope.row.taxCollectDate, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
                            </template>
                            <template v-else-if="columns.prop === 'newFollowDate'">
                                {{ parseTime(scope.row.newFollowDate, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
                            </template>
                            <template v-else-if="columns.prop === 'turnCustomerDate'">
                                {{ parseTime(scope.row.turnCustomerDate, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
                            </template>
                            <!-- 登记状态 -->
                            <template v-else-if="columns.prop === 'operationStatusLabel'">
                                <span v-if="!scope.row.operationStatusLabel" >-</span>
                                <span v-else-if="scope.row.operationStatusLabel.includes('存续')"  class="color-green" >{{ scope.row.operationStatusLabel }}</span>
                                <span 
                                    v-else-if="scope.row.operationStatusLabel.includes('迁入') || 
                                        scope.row.operationStatusLabel.includes('迁出') || 
                                        scope.row.operationStatusLabel.includes('注销') || 
                                        scope.row.operationStatusLabel.includes('吊销') || 
                                        scope.row.operationStatusLabel.includes('撤销')" 
                                    class="color-red" >
                                    {{ scope.row.operationStatusLabel }}
                                </span>
                                <span v-else class="color-three-grey">{{ scope.row.operationStatusLabel }}</span>
                            </template>
                            <!-- 风险类型 -->
                            <template v-else-if="columns.prop === 'riskTypeOverView'">
                                <div class="display-flex gap-4 text-ellipsis text-nowrap" v-if="scope.row.riskTypeOverView && scope.row.riskTypeOverView.length > 0">
                                    <div v-for="(item, index) in scope.row.riskTypeOverView" :key="index">
                                        <el-tag
                                            class="pointer "
                                            :type="riskType(item.label)"
                                            @click="showRisk(scope.row)"
                                        >
                                            {{ item.label }}×{{ item.num }}
                                        </el-tag>
                                    </div>
                                </div>
                                <el-tag class="ml-2" v-else type="info">暂无风险概览</el-tag>
                            </template>
                            <div v-else-if="columns.prop === 'action'" class="display-flex top-bottom-center space-between">
                                <a
                                    class="el-dropdown-link"
                                    style="text-decoration: none"
                                    @click="handleDetail(scope.row.id)"
                                >详情</a
                                >
                                <a class="el-dropdown-link" style="text-decoration: none" @click="startAIChat(scope.row)">
                                    AI分析
                                </a>
                                <el-dropdown trigger="click">
                                    <a class="el-dropdown-link" style="text-decoration: none">更多</a>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item>
                                                <el-button
                                                    class="margin-right-10"
                                                    type="primary"
                                                    @click="handleOpenContact(scope.row)"
                                                    text
                                                >联系方式</el-button
                                                >
                                            </el-dropdown-item>
                                            <el-dropdown-item>
                                                <el-button
                                                    class="margin-right-10"
                                                    type="primary"
                                                    @click="handleSeeReport(scope.row)"
                                                    text
                                                >查看报告</el-button
                                                >
                                            </el-dropdown-item>
                                            <el-dropdown-item v-if="activeName !== 'with'">
                                                <el-button
                                                    class="margin-right-10"
                                                    type="danger"
                                                    text
                                                    :disabled="!permissionService.isCompanyDeletePermitted()"
                                                    @click="handleDel([scope.row.id])"
                                                >删除</el-button
                                                >
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                            <span v-else>{{ scope.row[columns.prop] || '-' }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 分页器 -->
            <el-affix v-show="!tableLoading" target=".table-content" position="bottom" :offset="16" :z-index="2">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>

        <AddLeadDialog v-model:visible="addVisible" :from="'customer'" @refreshData="handleRefreshData"></AddLeadDialog>
        <MinistrantDialog
            v-if="ministrantVisible"
            :visible="ministrantVisible"
            :selectedLeadList="selectedData"
            @closeVisible="handleCloseMinistrantDialog"
        ></MinistrantDialog>
        <ContactListDialog
            v-if="contactListVisible"
            v-model:visible="contactListVisible"
            :companyInfo="checkedSCompanyInfo"
        />
        <ReportListDialog
            v-if="reportListVisible"
            v-model:visible="reportListVisible"
            :companyInfo="checkedSCompanyInfo"
        />
        <CrmDetailDrawer
            v-model:drawer="crmDetailDrawerFlag"
            v-if="crmDetailDrawerFlag"
            :crmId="crmId"
            :crmType="crmType"
            :isFromMinistration="activeName === 'with'"
            @refreshData="handleRefreshData"
        />
        <SendSmsDialog :visible="smsVisible" @closeVisible="handleCloseSmsVisible" :selectedData="selectedData" />
        <AddTagDialog v-model:visible="addTagVisible" @closeVisible="handleCloseAddTagVisible" :selectedData="selectedData" />
    </div>
    <RiskDialog v-model:visible="riskDialogVisible" :companyInfo="targetCompant"/>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
.action-bar {
    color: #303133;
    // margin-bottom: 16px;
    .choose-content {
        font-size: 14px;
        color: #a6a6a6;
        margin-right: 16px;
    }
}

.el-dropdown-link {
    display: block;
    cursor: pointer;
    color: var(--main-blue-);
    font-size: 16px;
    line-height: 23px;
}
:deep(.el-dropdown-menu__item) {
    padding: 2px 16px;
}
:deep(.el-table.is-scrolling-left th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-table__header-wrapper tr th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-button) {
    font-weight: 400;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}

.action-bar-fixed{
    position: fixed;
    top: 84px;
    right: 0px;
    background-color: #fff;
    z-index: 10;
    padding: 6px 16px;
    box-shadow: 0px 10px 10px #f0f0f0;
}
</style>
